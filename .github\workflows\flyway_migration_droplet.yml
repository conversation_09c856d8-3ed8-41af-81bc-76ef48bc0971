name: Deploy PostgreSQL DB

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy-db:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Copy files to Droplet
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_DEPLOY_USER }}
          key: ${{ secrets.DROPLET_DEPLOY_SSH_KEY }}
          source: "."
          target: "~/yezhome_db"

      - name: Deploy with Docker Compose
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_DEPLOY_USER }}
          key: ${{ secrets.DROPLET_DEPLOY_SSH_KEY }}
          script: |
            cd ~/yezhome_db
            docker compose pull
            docker compose up -d --build

            # Run Flyway migration from flyway container
            docker compose run --rm flyway migrate
