name: Deploy PostgreSQL DB to Digital Ocean Droplet

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy-db:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Create environment file
        run: |
          echo "DB_USER=${{ secrets.DB_USER }}" > .env
          echo "DB_PASSWORD=${{ secrets.DB_PASSWORD }}" >> .env
          echo "DB_NAME=${{ secrets.DB_NAME }}" >> .env

      - name: Copy files to Droplet
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_DEPLOY_USER }}
          key: ${{ secrets.DROPLET_DEPLOY_SSH_KEY }}
          source: "."
          target: "~/yezhome_db"
          rm: true

      - name: Deploy with Docker Compose
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_DEPLOY_USER }}
          key: ${{ secrets.DROPLET_DEPLOY_SSH_KEY }}
          script: |
            cd ~/yezhome_db

            # Ensure Docker and Docker Compose are available
            if ! command -v docker &> /dev/null; then
              echo "Docker not found. Please install Docker on the droplet."
              exit 1
            fi

            # Stop existing containers if running
            docker compose down || true

            # Pull latest images
            docker compose pull

            # Start PostgreSQL database
            docker compose up -d postgres

            # Wait for PostgreSQL to be ready
            echo "Waiting for PostgreSQL to be ready..."
            timeout 60 bash -c 'until docker compose exec postgres pg_isready -U ${{ secrets.DB_USER }}; do sleep 2; done'

            # Check if database exists, if not create it
            echo "Checking if database exists..."
            DB_EXISTS=$(docker compose exec postgres psql -U ${{ secrets.DB_USER }} -lqt | cut -d \| -f 1 | grep -qw ${{ secrets.DB_NAME }} && echo "exists" || echo "not_exists")

            if [ "$DB_EXISTS" = "not_exists" ]; then
              echo "Database does not exist. Creating database..."
              docker compose exec postgres createdb -U ${{ secrets.DB_USER }} ${{ secrets.DB_NAME }}

              echo "Running initial database setup (Init scripts)..."
              docker compose run --rm flyway

              if [ $? -eq 0 ]; then
                echo "Initial database setup completed successfully!"
              else
                echo "Initial database setup failed!"
                docker compose logs flyway
                exit 1
              fi
            else
              echo "Database already exists. Skipping initial setup."
            fi

            # Run Flyway migrations
            echo "Running Flyway migrations..."
            docker compose run --rm flyway-migrate

            # Check migration status
            if [ $? -eq 0 ]; then
              echo "Database migration completed successfully!"
            else
              echo "Database migration failed!"
              docker compose logs flyway-migrate
              exit 1
            fi
