# Tên của workflow, sẽ hiển thị trong tab Actions của GitHub
name: Deploy Flyway Migrations Neon

# C<PERSON><PERSON> hình sự kiện kích hoạt workflow
on:
  push:
    branches:
      - main  # Chạy workflow mỗi khi có commit mới lên nhánh 'main'

jobs:
  # Tên của job
  flyway-migrate-neon:
    # Môi trường thực thi mà job sẽ chạy trên đó
    runs-on: ubuntu-latest

    # Các bước thực thi của job
    steps:
      # Bước 1: Checkout code từ repository về máy ảo
      - name: Checkout code
        uses: actions/checkout@v4

      # Bước 2: Cài đặt môi trường Java (cần thiết cho Flyway)
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      # Bước 3: Chạy Flyway Migrate
      - name: Setup Redgate Flyway
        uses: red-gate/setup-flyway@v1.0.3
          
      - name: Run Flyway migrate
        run: |
          flyway \
          -url=jdbc:postgresql://${{ secrets.DB_HOST }}/${{ secrets.DB_NAME }}?sslmode=require\&channelBinding=require \
          -user=${{ secrets.DB_USER }} \
          -password=${{ secrets.DB_PASSWORD }} \
          -locations=filesystem:Scripts/Init,filesystem:Scripts/Migrations \
          -licenseKey=community \
          migrate
