# YEZHome Database Deployment Guide

This guide explains how to deploy a PostgreSQL database with Flyway migrations to a Digital Ocean Droplet using Docker and GitHub Actions.

## Prerequisites

### Digital Ocean Droplet Setup
1. Create a Digital Ocean Droplet with Ubuntu 22.04 or later
2. Install Docker and <PERSON><PERSON> Compose on the droplet:
   ```bash
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   
   # Install Docker Compose
   sudo apt-get update
   sudo apt-get install docker-compose-plugin
   ```
3. Create a deploy user (optional but recommended):
   ```bash
   sudo adduser deploy
   sudo usermod -aG docker deploy
   sudo usermod -aG sudo deploy
   ```

### GitHub Secrets Configuration
Set up the following secrets in your GitHub repository:

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `DROPLET_IP` | Your droplet's IP address | `*************` |
| `DROPLET_DEPLOY_USER` | SSH username for deployment | `deploy` or `root` |
| `DROPLET_DEPLOY_SSH_KEY` | Private SSH key for authentication | `-----BEGIN OPENSSH PRIVATE KEY-----...` |
| `DB_USER` | PostgreSQL username | `postgres` |
| `DB_PASSWORD` | PostgreSQL password | `your_secure_password` |
| `DB_NAME` | PostgreSQL database name | `yezhome` |

## Project Structure

```
YEZHome_DB/
├── .github/workflows/
│   └── flyway_migration_droplet.yml    # GitHub Actions workflow
├── Scripts/
│   └── Migrations/                     # Flyway migration files
├── docker-compose.yml                  # Docker services configuration
├── flyway-prod.conf                    # Flyway production configuration
├── .env.example                        # Environment variables template
└── DEPLOYMENT_README.md                # This file
```

## Deployment Process

### Automatic Deployment
The deployment happens automatically when you:
1. Push changes to the `main` branch
2. Manually trigger the workflow via GitHub Actions

### Manual Deployment
You can also deploy manually on your droplet:

1. Clone the repository:
   ```bash
   git clone <your-repo-url> ~/yezhome_db
   cd ~/yezhome_db
   ```

2. Create environment file:
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. Deploy:
   ```bash
   docker compose up -d postgres
   docker compose run --rm flyway
   ```

## Migration Files

Migration files should be placed in `Scripts/Migrations/` and follow Flyway naming convention:
- Format: `V{version}__{description}.sql`
- Example: `V20250817.01__CreateUserTable.sql`

## Troubleshooting

### Common Issues

1. **Docker not found on droplet**
   - Ensure Docker is installed and the user is in the docker group

2. **PostgreSQL connection failed**
   - Check if the database container is running: `docker compose ps`
   - Verify environment variables are set correctly

3. **Flyway migration failed**
   - Check migration file syntax
   - Review Flyway logs: `docker compose logs flyway`

4. **SSH connection failed**
   - Verify SSH key is correct and has proper permissions
   - Ensure the droplet IP and username are correct

### Useful Commands

```bash
# Check container status
docker compose ps

# View logs
docker compose logs postgres
docker compose logs flyway

# Connect to PostgreSQL
docker compose exec postgres psql -U $DB_USER -d $DB_NAME

# Run specific Flyway command
docker compose run --rm flyway info
docker compose run --rm flyway validate
```

## Security Considerations

1. Use strong passwords for database credentials
2. Limit SSH access to specific IP addresses if possible
3. Regularly update Docker images and system packages
4. Consider using a firewall to restrict database port access
5. Use environment variables for sensitive configuration

## Monitoring

After deployment, monitor:
- Database container health: `docker compose ps`
- Database logs: `docker compose logs postgres`
- Disk usage: `df -h`
- Memory usage: `free -h`
