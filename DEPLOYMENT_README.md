# YEZHome Database Deployment Guide

This guide explains how to deploy a PostgreSQL database with Flyway migrations to a Digital Ocean Droplet using Docker and GitHub Actions.

## Prerequisites

### Digital Ocean Droplet Setup
1. Create a Digital Ocean Droplet with Ubuntu 22.04 or later
2. Install Docker and <PERSON><PERSON> Compose on the droplet:
   ```bash
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   
   # Install Docker Compose
   sudo apt-get update
   sudo apt-get install docker-compose-plugin
   ```
3. Create a deploy user (optional but recommended):
   ```bash
   sudo adduser deploy
   sudo usermod -aG docker deploy
   sudo usermod -aG sudo deploy
   ```

### GitHub Secrets Configuration
Set up the following secrets in your GitHub repository:

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `DROPLET_IP` | Your droplet's IP address | `*************` |
| `DROPLET_DEPLOY_USER` | SSH username for deployment | `deploy` or `root` |
| `DROPLET_DEPLOY_SSH_KEY` | Private SSH key for authentication | `-----BEGIN OPENSSH PRIVATE KEY-----...` |
| `DB_USER` | PostgreSQL username | `postgres` |
| `DB_PASSWORD` | PostgreSQL password | `your_secure_password` |
| `DB_NAME` | PostgreSQL database name | `yezhome` |

## Project Structure

```
YEZHome_DB/
├── .github/workflows/
│   └── flyway_migration_droplet.yml    # GitHub Actions workflow
├── Scripts/
│   ├── create_db/                      # Database creation scripts
│   ├── Init/                           # Initial table creation and seed data
│   └── Migrations/                     # Ongoing schema migrations
├── docker-compose.yml                  # Docker services configuration
├── flyway-prod.conf                    # Flyway production configuration
├── .env.example                        # Environment variables template
├── deploy-local.sh                     # Local deployment script
├── manual-db-setup.sh                  # Manual setup script for troubleshooting
└── DEPLOYMENT_README.md                # This file
```

## Database Initialization Process

The deployment handles three phases of database setup:

1. **Database Creation**: Creates the PostgreSQL database if it doesn't exist
2. **Initial Setup** (`Scripts/Init/`): Creates tables, extensions, and seed data
3. **Migrations** (`Scripts/Migrations/`): Applies ongoing schema changes

### Script Execution Order:
1. `Scripts/Init/V000__CreateDB_Docker.sql` - Extensions and basic setup
2. `Scripts/Init/V001__CreateTables.sql` - Create all tables
3. `Scripts/Init/V002__InsertSeedData.sql` - Insert initial data
4. `Scripts/Init/V003__CreateTriggerFunction.sql` - Create triggers/functions
5. `Scripts/Migrations/V*.sql` - Apply all migrations in order

## Deployment Process

### Automatic Deployment
The deployment happens automatically when you:
1. Push changes to the `main` branch
2. Manually trigger the workflow via GitHub Actions

### Manual Deployment
You can also deploy manually on your droplet:

1. Clone the repository:
   ```bash
   git clone <your-repo-url> ~/yezhome_db
   cd ~/yezhome_db
   ```

2. Create environment file:
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. Deploy:
   ```bash
   # Option 1: Use the automated script
   ./deploy-local.sh

   # Option 2: Manual step-by-step
   docker compose up -d postgres
   docker compose run --rm flyway          # Run Init scripts
   docker compose run --rm flyway-migrate  # Run Migrations

   # Option 3: Full manual control (for troubleshooting)
   ./manual-db-setup.sh
   ```

## Manual Database Setup

If you need to manually set up the database (for troubleshooting or custom scenarios):

### For Fresh Database (No existing database):
```bash
# 1. Start PostgreSQL
docker compose up -d postgres

# 2. Create database
docker compose exec postgres createdb -U postgres yezhome

# 3. Run initial setup
docker compose run --rm flyway

# 4. Run migrations
docker compose run --rm flyway-migrate
```

### For Existing Database (Skip Init, run only Migrations):
```bash
# Start PostgreSQL
docker compose up -d postgres

# Run only migrations
docker compose run --rm flyway-migrate
```

### Manual Script Execution:
```bash
# Run individual SQL files manually
docker compose exec postgres psql -U postgres -d yezhome -f /path/to/script.sql

# Or copy and execute
docker cp Scripts/Init/V001__CreateTables.sql container_name:/tmp/
docker compose exec postgres psql -U postgres -d yezhome -f /tmp/V001__CreateTables.sql
```

## Migration Files

Migration files should be placed in `Scripts/Migrations/` and follow Flyway naming convention:
- Format: `V{version}__{description}.sql`
- Example: `V20250817.01__CreateUserTable.sql`

## Troubleshooting

### Common Issues

1. **Docker not found on droplet**
   - Ensure Docker is installed and the user is in the docker group

2. **PostgreSQL connection failed**
   - Check if the database container is running: `docker compose ps`
   - Verify environment variables are set correctly

3. **Flyway migration failed**
   - Check migration file syntax
   - Review Flyway logs: `docker compose logs flyway`

4. **SSH connection failed**
   - Verify SSH key is correct and has proper permissions
   - Ensure the droplet IP and username are correct

### Useful Commands

```bash
# Check container status
docker compose ps

# View logs
docker compose logs postgres
docker compose logs flyway

# Connect to PostgreSQL
docker compose exec postgres psql -U $DB_USER -d $DB_NAME

# Run specific Flyway command
docker compose run --rm flyway info
docker compose run --rm flyway validate
```

## Security Considerations

1. Use strong passwords for database credentials
2. Limit SSH access to specific IP addresses if possible
3. Regularly update Docker images and system packages
4. Consider using a firewall to restrict database port access
5. Use environment variables for sensitive configuration

## Monitoring

After deployment, monitor:
- Database container health: `docker compose ps`
- Database logs: `docker compose logs postgres`
- Disk usage: `df -h`
- Memory usage: `free -h`
