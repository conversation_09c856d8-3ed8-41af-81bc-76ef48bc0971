# Flyway Database Migration Guide for YEZHome Project

This project uses Flyway for database schema versioning and migration management.

## Configuration Files

- `flyway.conf` - Base Flyway configuration
- `flyway-dev.conf` - Development environment configuration
- `flyway-prod.conf` - Production environment configuration

## Prerequisites

1. **Install Flyway**
   - Download from: https://flywaydb.org/download/
   - Or use package manager: `choco install flyway` (Windows), `brew install flyway` (macOS)

2. **PostgreSQL JDBC Driver**
   - Download PostgreSQL JDBC driver and place it in Flyway's `drivers` folder
   - Or Flyway will auto-download it on first use

## Database Setup

The project uses PostgreSQL with the following structure:
- **Database Name**: `yezhome`
- **Owner**: `postgres`
- **Encoding**: UTF-8
- **Locale**: Vietnamese_Vietnam.1258

## Migration Structure

```
Scripts/
├── Init/           # Initial database setup scripts
│   ├── V000__CreateDB.sql
│   ├── V001__CreateTables.sql
│   ├── V002__InsertSeedData.sql
│   └── V003__CreateTriggerFunction.sql
└── Migrations/     # Version-controlled migration scripts
    ├── V20250614_01__AppUser_CreateIsActive.sql
    ├── V20250614_02__AppUser_InvoiceInformation.sql
    └── ...
```

## Usage Commands

### Basic Commands

```bash
# Check Flyway info and pending migrations
flyway info

# Run all pending migrations
flyway migrate

# Validate applied migrations against available ones
flyway validate

# Get detailed information about schema history
flyway info -detailed
```

### Environment-Specific Commands

```bash
# Development environment
flyway -configFiles=flyway-dev.conf migrate

# Production environment
flyway -configFiles=flyway-prod.conf migrate
```

### Database Management Commands

```bash
# Baseline existing database (for first-time setup)
flyway baseline

# Clean database (CAUTION: Drops all objects in schema)
flyway clean

# Repair schema history table
flyway repair
```

## Configuration Details

### Database Connection
Update the following in your environment-specific config files:
- `flyway.url` - Database connection URL
- `flyway.user` - Database username
- `flyway.password` - Database password (use environment variables for production)

### Migration Locations
The configuration includes both initial setup and migration scripts:
- `Scripts/Init` - Database initialization scripts
- `Scripts/Migrations` - Incremental migration scripts

### Naming Convention
Migration files follow Flyway's naming convention:
- **Format**: `V<version>__<description>.sql`
- **Examples**: 
  - `V20250614_01__AppUser_CreateIsActive.sql`
  - `V20250615__CreateUserAvatar_Table.sql`

## Environment Variables (Recommended for Production)

```bash
# Set environment variables for production
export FLYWAY_PASSWORD=your_secure_password
export FLYWAY_URL=******************************************
```

## Best Practices

1. **Never modify applied migrations** - Create new migrations instead
2. **Test migrations** in development before applying to production
3. **Backup database** before running migrations in production
4. **Use descriptive names** for migration files
5. **Keep migrations small** and focused on single changes
6. **Version control** all migration scripts

## Troubleshooting

### Common Issues

1. **Connection refused**: Check PostgreSQL service and connection details
2. **Permission denied**: Ensure database user has necessary privileges
3. **Migration checksum mismatch**: Use `flyway repair` if migration was accidentally modified
4. **Out of order migrations**: Set `flyway.outOfOrder=true` if needed (not recommended for production)

### Useful Commands for Debugging

```bash
# Check current schema state
flyway info -detailed

# Validate all migrations
flyway validate

# Repair schema history
flyway repair

# Clean and rebuild (development only)
flyway clean
flyway migrate
```

## Integration with Development Workflow

1. **Creating new migrations**:
   - Create SQL file with proper naming convention
   - Test in development environment
   - Commit to version control

2. **Deployment process**:
   - Pull latest migrations
   - Run `flyway migrate` on target environment
   - Verify with `flyway info`

For more information, visit the [Flyway Documentation](https://flywaydb.org/documentation/).