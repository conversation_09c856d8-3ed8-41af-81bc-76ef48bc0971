-- Add Personal Tax Code column to AppUser table
ALTER TABLE "AppUser"
    ADD COLUMN "PersonalTaxCode" VARCHAR(14);

-- Add Invoice Information columns to AppUser table
ALTER TABLE "AppUser"
    ADD COLUMN "InvoiceBuyerName" VARCHAR(255),
    ADD COLUMN "InvoiceEmail" VARCHAR(255),
    ADD COLUMN "InvoiceCompanyName" VARCHAR(255),
    ADD COLUMN "InvoiceTaxCode" VARCHAR(14),
    ADD COLUMN "InvoiceAddress" TEXT;

-- Add constraint to validate Personal Tax Code format (10 or 13 digits)
ALTER TABLE "AppUser"
    ADD CONSTRAINT "CK_AppUser_PersonalTaxCode" 
    CHECK (
        "PersonalTaxCode" IS NULL OR 
        (LENGTH("PersonalTaxCode") = 10 AND "PersonalTaxCode" ~ '^[0-9]{10}$') OR 
        (LENGTH("PersonalTaxCode") = 14 AND "PersonalTaxCode" ~ '^[0-9]{10}-[0-9]{3}$')
    );

-- Add constraint to validate Invoice Tax Code format (10 or 13 digits)
ALTER TABLE "AppUser"
    ADD CONSTRAINT "CK_AppUser_InvoiceTaxCode" 
    CHECK (
        "InvoiceTaxCode" IS NULL OR 
        (LENGTH("InvoiceTaxCode") = 10 AND "InvoiceTaxCode" ~ '^[0-9]{10}$') OR 
        (LENGTH("InvoiceTaxCode") = 14 AND "InvoiceTaxCode" ~ '^[0-9]{10}-[0-9]{3}$')
    );

-- Add comments for better documentation
COMMENT ON COLUMN "AppUser"."PersonalTaxCode" IS 'Personal Tax Code (10 or 13 digits)';
COMMENT ON COLUMN "AppUser"."InvoiceBuyerName" IS 'Name to appear on invoices';
COMMENT ON COLUMN "AppUser"."InvoiceEmail" IS 'Email to send invoices to';
COMMENT ON COLUMN "AppUser"."InvoiceCompanyName" IS 'Company name for invoices';
COMMENT ON COLUMN "AppUser"."InvoiceTaxCode" IS 'Tax code for invoices (10 or 13 digits)';
COMMENT ON COLUMN "AppUser"."InvoiceAddress" IS 'Address for invoices';
