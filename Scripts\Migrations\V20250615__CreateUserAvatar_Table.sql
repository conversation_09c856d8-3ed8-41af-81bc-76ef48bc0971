-- Create UserAvatar table for storing user avatar images
CREATE TABLE IF NOT EXISTS "UserAvatar" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "UserID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "MediaType" VARCHAR(20) NOT NULL,
    "MediaURL" VARCHAR(500) NOT NULL,
    "UploadedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "Caption" VARCHAR(50),
    "FilePath" TEXT NULL,
    "ThumbnailPath" TEXT NULL,
    "SmallPath" TEXT NULL,
    "MediumPath" TEXT NULL,
    "LargePath" TEXT NULL
);

-- Create index for faster queries
CREATE INDEX "IDX_UserAvatar_UserID" ON "UserAvatar"("UserID");
