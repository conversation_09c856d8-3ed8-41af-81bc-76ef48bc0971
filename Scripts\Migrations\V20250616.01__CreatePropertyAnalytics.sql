-- Create PropertyViewLog table to track property views
CREATE TABLE IF NOT EXISTS "PropertyViewLog" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "PropertyId" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "ViewerId" UUID REFERENCES "AppUser"("Id"),
    "ViewerIP" VARCHAR(50),
    "ViewedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UserAgent" TEXT,
    "ReferrerUrl" TEXT,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP
);

-- Create indexes for PropertyViewLog
CREATE INDEX "IDX_PropertyViewLog_PropertyId" ON "PropertyViewLog"("PropertyId");
CREATE INDEX "IDX_PropertyViewLog_ViewerId" ON "PropertyViewLog"("ViewerId");
CREATE INDEX "IDX_PropertyViewLog_ViewedAt" ON "PropertyViewLog"("ViewedAt");

-- Create PropertyEngagementSummary table for caching aggregated data
CREATE TABLE IF NOT EXISTS "PropertyEngagementSummary" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "PropertyId" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "TotalViews" INTEGER NOT NULL DEFAULT 0,
    "TotalFavorites" INTEGER NOT NULL DEFAULT 0,
    "TotalSpent" NUMERIC(20,2) NOT NULL DEFAULT 0,
    "ExtensionSpent" NUMERIC(20,2) NOT NULL DEFAULT 0,
    "HighlightSpent" NUMERIC(20,2) NOT NULL DEFAULT 0,
    "LastUpdatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP
);

-- Create unique index on PropertyId to ensure one summary per property
CREATE UNIQUE INDEX "IDX_PropertyEngagementSummary_PropertyId" ON "PropertyEngagementSummary"("PropertyId");

-- Add comments for better documentation
COMMENT ON TABLE "PropertyViewLog" IS 'Logs each view of a property listing';
COMMENT ON TABLE "PropertyEngagementSummary" IS 'Cached summary of property engagement metrics for faster reporting';