﻿
CREATE TABLE IF NOT EXISTS public."PropertyHistory" (
    "HistoryId" BIGSERIAL PRIMARY KEY,
    "PropertyId" UUID NOT NULL, -- ID của tài sản đã đư<PERSON><PERSON> cập nhật, gi<PERSON><PERSON> liên kết với bảng Property
    "ColumnName" VARCHAR(255) NOT NULL, -- Tên của cột đã thay đổi (ví dụ: 'Name', 'Description', 'Price').
    "OldValue" TEXT, -- G<PERSON><PERSON> trị cũ của cột trước khi cập nhật. Kiểu TEXT phù hợp để lưu trữ nhiều loại dữ liệu.
    "NewValue" TEXT, -- Gi<PERSON> trị mới của cột sau khi cập nhật. Kiểu TEXT phù hợp để lưu trữ nhiều loại dữ liệu.
    "ChangedBy" UUID, -- ID của người dùng đã thực hiện thay đổi, gi<PERSON><PERSON> theo dõi ai đã cập nhật tài sản.
    "ChangedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL -- Thời gian thay đổi, mặc định là thời điểm hiện tại.
);

-- Thêm index để tối ưu truy vấn
CREATE INDEX idx_propertyhistory_propertyid ON public."PropertyHistory" ("PropertyId");
CREATE INDEX idx_propertyhistory_changedat ON public."PropertyHistory" ("ChangedAt");
CREATE INDEX idx_propertyhistory_columnname ON public."PropertyHistory" ("ColumnName");

/*
Tạo Trigger Function (log_property_changes)
Hàm này sẽ so sánh giá trị cũ và mới của từng cột quan trọng trong bảng Property. Nếu có bất kỳ thay đổi nào, nó sẽ chèn một bản ghi vào bảng PropertyHistory.
*/

CREATE OR REPLACE FUNCTION public.log_property_changes()
RETURNS TRIGGER AS $$
DECLARE
    v_column_name TEXT;
    v_old_value TEXT;
    v_new_value TEXT;
BEGIN
    -- Hàm này sẽ được gọi SAU mỗi lần UPDATE trên bảng "Property".
    -- OLD.* chứa giá trị của hàng trước khi UPDATE.
    -- NEW.* chứa giá trị của hàng sau khi UPDATE.

    -- Ghi log cho cột "Name"
    IF OLD."Name" IS DISTINCT FROM NEW."Name" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Name', OLD."Name", NEW."Name", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Slug"
    IF OLD."Slug" IS DISTINCT FROM NEW."Slug" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Slug', OLD."Slug", NEW."Slug", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "PostType"
    IF OLD."PostType" IS DISTINCT FROM NEW."PostType" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'PostType', OLD."PostType", NEW."PostType", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "PropertyType"
    IF OLD."PropertyType" IS DISTINCT FROM NEW."PropertyType" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'PropertyType', OLD."PropertyType", NEW."PropertyType", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "CityId"
    IF OLD."CityId" IS DISTINCT FROM NEW."CityId" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'CityId', OLD."CityId"::TEXT, NEW."CityId"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "DistrictId"
    IF OLD."DistrictId" IS DISTINCT FROM NEW."DistrictId" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'DistrictId', OLD."DistrictId"::TEXT, NEW."DistrictId"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "StreetId"
    IF OLD."StreetId" IS DISTINCT FROM NEW."StreetId" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'StreetId', OLD."StreetId"::TEXT, NEW."StreetId"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "WardId"
    IF OLD."WardId" IS DISTINCT FROM NEW."WardId" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'WardId', OLD."WardId"::TEXT, NEW."WardId"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Address"
    IF OLD."Address" IS DISTINCT FROM NEW."Address" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Address', OLD."Address", NEW."Address", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Area"
    IF OLD."Area" IS DISTINCT FROM NEW."Area" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Area', OLD."Area"::TEXT, NEW."Area"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Price"
    IF OLD."Price" IS DISTINCT FROM NEW."Price" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Price', OLD."Price"::TEXT, NEW."Price"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Latitude"
    IF OLD."Latitude" IS DISTINCT FROM NEW."Latitude" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Latitude', OLD."Latitude"::TEXT, NEW."Latitude"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Longitude"
    IF OLD."Longitude" IS DISTINCT FROM NEW."Longitude" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Longitude', OLD."Longitude"::TEXT, NEW."Longitude"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "VideoUrl"
    IF OLD."VideoUrl" IS DISTINCT FROM NEW."VideoUrl" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'VideoUrl', OLD."VideoUrl", NEW."VideoUrl", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Floors"
    IF OLD."Floors" IS DISTINCT FROM NEW."Floors" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Floors', OLD."Floors"::TEXT, NEW."Floors"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Rooms"
    IF OLD."Rooms" IS DISTINCT FROM NEW."Rooms" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Rooms', OLD."Rooms"::TEXT, NEW."Rooms"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Toilets"
    IF OLD."Toilets" IS DISTINCT FROM NEW."Toilets" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Toilets', OLD."Toilets"::TEXT, NEW."Toilets"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Direction"
    IF OLD."Direction" IS DISTINCT FROM NEW."Direction" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Direction', OLD."Direction", NEW."Direction", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "BalconyDirection"
    IF OLD."BalconyDirection" IS DISTINCT FROM NEW."BalconyDirection" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'BalconyDirection', OLD."BalconyDirection", NEW."BalconyDirection", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Legality"
    IF OLD."Legality" IS DISTINCT FROM NEW."Legality" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Legality', OLD."Legality", NEW."Legality", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Interior"
    IF OLD."Interior" IS DISTINCT FROM NEW."Interior" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Interior', OLD."Interior", NEW."Interior", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Width"
    IF OLD."Width" IS DISTINCT FROM NEW."Width" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Width', OLD."Width"::TEXT, NEW."Width"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "RoadWidth"
    IF OLD."RoadWidth" IS DISTINCT FROM NEW."RoadWidth" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'RoadWidth', OLD."RoadWidth"::TEXT, NEW."RoadWidth"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Description"
    IF OLD."Description" IS DISTINCT FROM NEW."Description" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Description', OLD."Description", NEW."Description", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Overview"
    IF OLD."Overview" IS DISTINCT FROM NEW."Overview" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Overview', OLD."Overview", NEW."Overview", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "PlaceData"
    IF OLD."PlaceData" IS DISTINCT FROM NEW."PlaceData" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'PlaceData', OLD."PlaceData", NEW."PlaceData", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Policies"
    IF OLD."Policies" IS DISTINCT FROM NEW."Policies" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Policies', OLD."Policies", NEW."Policies", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Neighborhood"
    IF OLD."Neighborhood" IS DISTINCT FROM NEW."Neighborhood" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Neighborhood', OLD."Neighborhood", NEW."Neighborhood", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "Status"
    IF OLD."Status" IS DISTINCT FROM NEW."Status" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'Status', OLD."Status", NEW."Status", NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "IsDeleted" (quan trọng để theo dõi khi bản ghi bị xóa mềm)
    IF OLD."IsDeleted" IS DISTINCT FROM NEW."IsDeleted" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'IsDeleted', OLD."IsDeleted"::TEXT, NEW."IsDeleted"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "DeletedAt" (khi IsDeleted chuyển sang TRUE)
    IF OLD."DeletedAt" IS DISTINCT FROM NEW."DeletedAt" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'DeletedAt', OLD."DeletedAt"::TEXT, NEW."DeletedAt"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "IsHighlighted"
    IF OLD."IsHighlighted" IS DISTINCT FROM NEW."IsHighlighted" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'IsHighlighted', OLD."IsHighlighted"::TEXT, NEW."IsHighlighted"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "IsAutoRenew"
    IF OLD."IsAutoRenew" IS DISTINCT FROM NEW."IsAutoRenew" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'IsAutoRenew', OLD."IsAutoRenew"::TEXT, NEW."IsAutoRenew"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Ghi log cho cột "UpdateRemainingTimes"
    IF OLD."UpdateRemainingTimes" IS DISTINCT FROM NEW."UpdateRemainingTimes" THEN
        INSERT INTO public."PropertyHistory" ("PropertyId", "ColumnName", "OldValue", "NewValue", "ChangedBy", "ChangedAt")
        VALUES (OLD."Id", 'UpdateRemainingTimes', OLD."UpdateRemainingTimes"::TEXT, NEW."UpdateRemainingTimes"::TEXT, NEW."UpdatedBy", NOW());
    END IF;

    -- Luôn trả về NEW để hoàn thành thao tác UPDATE
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


/*
Tạo Trigger (property_update_history)
Trigger này sẽ kích hoạt hàm log_property_changes mỗi khi có một bản ghi trong bảng Property được cập nhật.
*/
CREATE TRIGGER property_update_history
AFTER UPDATE ON public."Property"
FOR EACH ROW
EXECUTE FUNCTION public.log_property_changes();