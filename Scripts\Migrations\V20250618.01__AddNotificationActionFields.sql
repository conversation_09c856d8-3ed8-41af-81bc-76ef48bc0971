-- Migration: Add action fields to Notification table
-- Date: 2025-01-19
-- Description: Add RelatedEntityId, RelatedPropertyId, and ActionUrl fields to support actionable notifications

-- Add new columns to Notification table
ALTER TABLE "Notification" 
ADD COLUMN "RelatedEntityId" UUID NULL,
ADD COLUMN "RelatedPropertyId" UUID NULL,
ADD COLUMN "ActionUrl" VARCHAR(500) NULL;

-- Add comments for documentation
COMMENT ON COLUMN "Notification"."RelatedEntityId" IS 'ID of related entity (contact request, transaction, etc.)';
COMMENT ON COLUMN "Notification"."RelatedPropertyId" IS 'Property ID for property-related notifications';
COMMENT ON COLUMN "Notification"."ActionUrl" IS 'URL for direct navigation when notification is clicked';

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS "IX_Notification_RelatedEntityId" ON "Notification" ("RelatedEntityId");
CREATE INDEX IF NOT EXISTS "IX_Notification_RelatedPropertyId" ON "Notification" ("RelatedPropertyId");

COMMIT;
