-- Step 1: Rename the foreign key constraint that references AgentId
-- This is crucial to avoid issues when renaming the column itself.
ALTER TABLE IF EXISTS public."ContactRequest"
RENAME CONSTRAINT "ContactRequest_AgentId_fkey" TO "ContactRequest_PropertyOwnerId_fkey";

-- Step 2: Rename the column AgentId to PropertyOwnerId
ALTER TABLE IF EXISTS public."ContactRequest"
RENAME COLUMN "AgentId" TO "PropertyOwnerId";

-- Step 3: Rename the associated index for the old AgentId column
-- This ensures your indexes remain consistent with the new column name.
ALTER INDEX IF EXISTS public."IDX_ContactRequest_Agent"
RENAME TO "IDX_ContactRequest_PropertyOwner";

-- Optional: You might want to update the comment if you use them for documentation
COMMENT ON COLUMN public."ContactRequest"."PropertyOwnerId"
    IS 'ID of the property owner or agent associated with the request';