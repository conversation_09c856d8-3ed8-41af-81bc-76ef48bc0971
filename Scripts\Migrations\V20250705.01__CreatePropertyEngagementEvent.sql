-- =====================================
-- ✅ 1. Schema: PropertyEngagementEvents
-- =====================================
CREATE TABLE IF NOT EXISTS public."PropertyEngagementEvents" (
    "Id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    "PropertyId" uuid NOT NULL,
    "UserId" uuid,
    "EventType" varchar(50) NOT NULL, -- view, favorite, click_phone, chat, etc.
    "SessionId" varchar(255), -- Optional: to detect unique sessions
    "DeviceId" varchar(255), -- Optional: to detect unique devices
    "UserAgent" text,
    "IpAddress" varchar(50),
    "DeviceType" varchar(20),   -- mobile, desktop, tablet
    "Platform" varchar(50),     -- iOS, Android, Windows...
    "Browser" varchar(50),      -- Chrome, Safari, etc.
    "City" varchar(100),        -- Optional
    "Region" varchar(100),
    "Country" varchar(100),
    "CreatedAt" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE public."PropertyEngagementEvents" IS 'Detailed event logs for property interactions (clicks, favorites, chat, etc)';

CREATE INDEX IF NOT EXISTS "IDX_PropertyEngagementEvents_PropertyId" ON public."PropertyEngagementEvents" ("PropertyId");

CREATE INDEX IF NOT EXISTS "IDX_PropertyEngagementEvents_EventType" ON public."PropertyEngagementEvents" ("EventType");

CREATE INDEX IF NOT EXISTS "IDX_PropertyEngagementEvents_CreatedAt" ON public."PropertyEngagementEvents" ("CreatedAt");