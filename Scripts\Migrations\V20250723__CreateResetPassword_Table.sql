CREATE TABLE public."PasswordResetToken" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NOT NULL,
    "Token" VARCHAR(255) NOT NULL UNIQUE,
    "ExpirationTime" TIM<PERSON><PERSON>MP WITH TIME ZONE NOT NULL,
    "IsUsed" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX IX_PasswordResetToken_UserId ON public."PasswordResetToken" ("UserId");
CREATE UNIQUE INDEX UX_PasswordResetToken_Token ON public."PasswordResetToken" ("Token");  