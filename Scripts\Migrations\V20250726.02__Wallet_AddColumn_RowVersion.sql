-- add column row version to table wallet 
-- reduce Concurrency Exception
ALTER TABLE "Wallets"
ADD COLUMN "RowVersion" INTEGER NOT NULL DEFAULT 0;

-- ## Bước 2: Tạo Function để tự động cập nhật RowVersion
CREATE OR REPLACE FUNCTION update_row_version_function()
RETURNS TRIGGER AS $$
BEGIN
   -- Tăng giá trị của cột RowVersion lên 1 so với giá trị cũ
   NEW."RowVersion" = OLD."RowVersion" + 1;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- ## Bước 3: Tạo Trigger để tự động cập nhật RowVersion
CREATE TRIGGER wallets_row_version_trigger
BEFORE UPDATE ON "Wallets"
FOR EACH ROW
EXECUTE FUNCTION update_row_version_function();