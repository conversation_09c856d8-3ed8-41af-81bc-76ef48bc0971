-- Create PropertySpendingLog table to track property-related spending
CREATE TABLE IF NOT EXISTS "SpendingLog" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "PropertyId" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "UserId" UUID NOT NULL REFERENCES "AppUser"("Id"),
    "Amount" NUMERIC(20,2) NOT NULL,
    "SpendingType" VARCHAR(50) NOT NULL, -- 'renew', 'highlight', etc.
    "TransactionId" UUID REFERENCES "WalletTransactions"("Id"),
    "SpentAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "Details" JSONB, -- For storing additional details like duration, highlight type, etc.
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP
);

-- Create indexes for SpendingLog
CREATE INDEX "IDX_SpendingLog_PropertyId" ON "SpendingLog"("PropertyId");
CREATE INDEX "IDX_SpendingLog_UserId" ON "SpendingLog"("UserId");
CREATE INDEX "IDX_SpendingLog_SpendingType" ON "SpendingLog"("SpendingType");

COMMENT ON TABLE "SpendingLog" IS 'Logs spending related to property listings (renew, highlight, etc.)';
COMMENT ON COLUMN "SpendingLog"."SpendingType" IS 'Type of spending: renew, highlight, etc.';
COMMENT ON COLUMN "SpendingLog"."Details" IS 'JSON with additional details about the spending';