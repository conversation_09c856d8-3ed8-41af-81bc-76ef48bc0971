#!/bin/bash

# Local deployment script for YEZHome Database
# This script helps test the deployment locally before pushing to production

set -e

echo "🚀 Starting YEZHome Database Local Deployment"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp .env.example .env
    echo "📝 Please edit .env file with your database credentials"
    echo "   Default values are set for local development"
fi

# Load environment variables
source .env

echo "📊 Using database configuration:"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo "   Password: [HIDDEN]"

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker compose down || true

# Start PostgreSQL
echo "🐘 Starting PostgreSQL database..."
docker compose up -d postgres

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
timeout 60 bash -c "until docker compose exec postgres pg_isready -U $DB_USER -d $DB_NAME; do sleep 2; done"

if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL is ready!"
else
    echo "❌ PostgreSQL failed to start within 60 seconds"
    docker compose logs postgres
    exit 1
fi

# Run Flyway migrations
echo "🔄 Running Flyway migrations..."
docker compose run --rm flyway

if [ $? -eq 0 ]; then
    echo "✅ Database migration completed successfully!"
    echo "🎉 YEZHome Database is ready!"
    echo ""
    echo "📋 Connection details:"
    echo "   Host: localhost"
    echo "   Port: 5432"
    echo "   Database: $DB_NAME"
    echo "   Username: $DB_USER"
    echo ""
    echo "🔧 Useful commands:"
    echo "   Connect to DB: docker compose exec postgres psql -U $DB_USER -d $DB_NAME"
    echo "   View logs: docker compose logs postgres"
    echo "   Stop services: docker compose down"
else
    echo "❌ Database migration failed!"
    echo "📋 Checking Flyway logs..."
    docker compose logs flyway
    exit 1
fi
