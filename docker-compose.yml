services:
  postgres:
    image: postgres:17
    container_name: yezhome-postgres
    restart: always
    environment:
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-123456}
      POSTGRES_DB: ${DB_NAME:-yezhome}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-yezhome}"]
      interval: 10s
      timeout: 5s
      retries: 5

  flyway:
    image: flyway/flyway:latest
    container_name: yezhome-flyway
    command: -url=*******************************/${DB_NAME:-yezhome} -user=${DB_USER:-postgres} -password=${DB_PASSWORD:-123456} -locations=filesystem:/flyway/sql migrate
    volumes:
      - ./Scripts/Init:/flyway/sql
      - ./Scripts/Migrations:/flyway/migrations
      - ./flyway-prod.conf:/flyway/conf/flyway.conf
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - FLYWAY_PASSWORD=${DB_PASSWORD:-123456}

  flyway-migrate:
    image: flyway/flyway:latest
    container_name: yezhome-flyway-migrate
    command: -url=*******************************/${DB_NAME:-yezhome} -user=${DB_USER:-postgres} -password=${DB_PASSWORD:-123456} -locations=filesystem:/flyway/sql migrate
    volumes:
      - ./Scripts/Migrations:/flyway/sql
      - ./flyway-prod.conf:/flyway/conf/flyway.conf
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - FLYWAY_PASSWORD=${DB_PASSWORD:-123456}
    profiles:
      - migrate

volumes:
  postgres_data:
