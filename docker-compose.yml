version: '3.9'
services:
  postgres:
    image: postgres:17
    container_name: yezhome-postgres
    restart: always
    environment:
      POSTGRES_USER: ${{ secrets.DB_USER }}
      POSTGRES_PASSWORD: ${{ secrets.DB_PASSWORD }}
      POSTGRES_DB: ${{ secrets.DB_NAME }}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  flyway:
    image: flyway/flyway:latest
    container_name: yezhome-flyway
    command: -url=*******************************/${{ secrets.DB_NAME }} -user=${{ secrets.DB_USER }} -password=${{ secrets.DB_PASSWORD }} migrate
    volumes:
      - ./migrations:/flyway/sql
    depends_on:
      - postgres

volumes:
  postgres_data:
