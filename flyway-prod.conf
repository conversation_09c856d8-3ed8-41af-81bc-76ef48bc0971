# Flyway Production Environment Configuration
# This file is used for Docker deployment to Digital Ocean Droplet

# Database connection settings (will be overridden by command line args)
flyway.url=***************************************
flyway.user=postgres
flyway.password=${FLYWAY_PASSWORD}

# Migration settings
flyway.locations=filesystem:/flyway/sql
flyway.schemas=public
flyway.table=flyway_schema_history

# Validation settings
flyway.validateOnMigrate=true
flyway.cleanOnValidationError=false

# Production-specific settings (more restrictive)
flyway.cleanDisabled=true
flyway.baselineOnMigrate=false
flyway.outOfOrder=false

# Placeholder settings
flyway.placeholderReplacement=true
flyway.placeholderPrefix=${
flyway.placeholderSuffix=}

# Output settings
flyway.outputType=json
flyway.outputQueryResults=false

# Encoding
flyway.encoding=UTF-8

# Connect retries
flyway.connectRetries=10
flyway.connectRetriesInterval=10