# Flyway Production Environment Configuration
# This file extends the base flyway.conf for production environment

# Include base configuration
flyway.configFiles=flyway.conf

# Production database connection
flyway.url=***********************************************
flyway.user=postgres
flyway.password=${FLYWAY_PASSWORD}

# Production-specific settings (more restrictive)
flyway.cleanDisabled=true
flyway.baselineOnMigrate=false
flyway.validateOnMigrate=true
flyway.outOfOrder=false

# Disable output for production
flyway.outputQueryResults=false