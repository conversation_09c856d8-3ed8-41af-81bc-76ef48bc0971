# Flyway Configuration File for YEZHome Database Project
# PostgreSQL Database Configuration

# Database connection settings
flyway.url=********************************************
flyway.user=postgres
flyway.password=123456

# Migration settings
flyway.locations=filesystem:Scripts/init,filesystem:Scripts/migrations
flyway.schemas=public
flyway.table=flyway_schema_history

# Validation settings
flyway.validateOnMigrate=true
flyway.cleanOnValidationError=false

# Baseline settings (useful for existing databases)
flyway.baselineOnMigrate=false
flyway.baselineVersion=1
flyway.baselineDescription=Initial baseline

# Placeholder settings
flyway.placeholderReplacement=true
flyway.placeholderPrefix=${
flyway.placeholderSuffix=}

# Output settings
flyway.outputType=json

# Mixed migration settings
flyway.mixed=false

# Group migrations
flyway.group=false

# Encoding
flyway.encoding=UTF-8

# Connect retries
flyway.connectRetries=0
