#!/bin/bash

# Manual Database Setup Script
# Use this script when you need to manually run database creation and initialization
# This is useful for troubleshooting or when the automatic process fails

set -e

echo "🔧 Manual YEZHome Database Setup"

# Load environment variables
if [ -f .env ]; then
    source .env
    echo "✅ Loaded environment variables from .env"
else
    echo "⚠️  .env file not found. Using defaults..."
    DB_USER=${DB_USER:-postgres}
    DB_PASSWORD=${DB_PASSWORD:-123456}
    DB_NAME=${DB_NAME:-yezhome}
fi

echo "📊 Database Configuration:"
echo "   User: $DB_USER"
echo "   Database: $DB_NAME"
echo "   Password: [HIDDEN]"

# Ensure PostgreSQL is running
echo "🐘 Starting PostgreSQL..."
docker compose up -d postgres

# Wait for PostgreSQL
echo "⏳ Waiting for PostgreSQL to be ready..."
timeout 60 bash -c "until docker compose exec postgres pg_isready -U $DB_USER; do sleep 2; done"

# Step 1: Create database if it doesn't exist
echo "🆕 Step 1: Creating database (if needed)..."
docker compose exec postgres psql -U $DB_USER -c "SELECT 1 FROM pg_database WHERE datname='$DB_NAME'" | grep -q 1 || \
docker compose exec postgres createdb -U $DB_USER $DB_NAME

echo "✅ Database '$DB_NAME' is ready"

# Step 2: Run create_db scripts (if any additional setup needed)
echo "🏗️  Step 2: Running create_db scripts..."
if [ -d "Scripts/create_db" ] && [ "$(ls -A Scripts/create_db)" ]; then
    for sql_file in Scripts/create_db/*.sql; do
        if [ -f "$sql_file" ]; then
            echo "   Executing: $(basename $sql_file)"
            docker compose exec -T postgres psql -U $DB_USER -d $DB_NAME < "$sql_file" || echo "   ⚠️  Warning: $(basename $sql_file) may have failed"
        fi
    done
else
    echo "   No create_db scripts found, skipping..."
fi

# Step 3: Run Init scripts with Flyway
echo "🏗️  Step 3: Running Init scripts..."
docker compose run --rm flyway

if [ $? -eq 0 ]; then
    echo "✅ Init scripts completed successfully!"
else
    echo "❌ Init scripts failed!"
    docker compose logs flyway
    echo "🔧 You can check the logs above and try running individual scripts manually"
    exit 1
fi

# Step 4: Run Migrations
echo "🔄 Step 4: Running Migrations..."
docker compose run --rm flyway-migrate

if [ $? -eq 0 ]; then
    echo "✅ Migrations completed successfully!"
else
    echo "❌ Migrations failed!"
    docker compose logs flyway-migrate
    echo "🔧 You can check the logs above and try running individual migrations manually"
    exit 1
fi

echo "🎉 Manual database setup completed successfully!"
echo ""
echo "📋 Connection details:"
echo "   Host: localhost"
echo "   Port: 5432"
echo "   Database: $DB_NAME"
echo "   Username: $DB_USER"
echo ""
echo "🔧 Useful commands:"
echo "   Connect: docker compose exec postgres psql -U $DB_USER -d $DB_NAME"
echo "   Check tables: docker compose exec postgres psql -U $DB_USER -d $DB_NAME -c '\\dt'"
echo "   Flyway info: docker compose run --rm flyway-migrate info"
